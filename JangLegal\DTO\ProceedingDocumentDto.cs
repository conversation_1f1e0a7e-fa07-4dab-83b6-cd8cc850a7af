using System.ComponentModel.DataAnnotations;

namespace JangLegal.DTO;

public class ProceedingDocumentDto
{
    public int DocumentId { get; set; }
    
    [Required(ErrorMessage = "Proceeding ID is required")]
    public int ProceedingId { get; set; }
    
    [Required(ErrorMessage = "Document type is required")]
    [StringLength(100, ErrorMessage = "Document type cannot be longer than 100 characters")]
    public string DocumentType { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Document title is required")]
    [StringLength(255, ErrorMessage = "Document title cannot be longer than 255 characters")]
    public string DocumentTitle { get; set; } = string.Empty;
    
    
    public string FilePath { get; set; } = string.Empty;
    
    [Required(ErrorMessage = "Submission date is required")]
    public DateOnly SubmissionDate { get; set; }
    
    [StringLength(255, ErrorMessage = "Submitted by cannot be longer than 255 characters")]
    public string SubmittedBy { get; set; } = string.Empty;
    
    [StringLength(1000, ErrorMessage = "Remarks cannot be longer than 1000 characters")]
    public string Remarks { get; set; } = string.Empty;
    
    public DateTime? CreatedDate { get; set; }
    public DateTime? ModifiedDate { get; set; }
    
    // Additional properties for file handling
    public string FileName { get; set; } = string.Empty;
    public string FileExtension { get; set; } = string.Empty;
    public long FileSizeBytes { get; set; }
    public string FileUrl { get; set; } = string.Empty;
    
    // Computed properties for display
    public string SubmissionDateFormatted => SubmissionDate.ToString("dd MMM, yyyy");
    public string FileSizeFormatted => FormatFileSize(FileSizeBytes);
    
    private static string FormatFileSize(long bytes)
    {
        if (bytes == 0) return "0 B";
        
        string[] sizes = { "B", "KB", "MB", "GB" };
        int order = 0;
        double size = bytes;
        
        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }
        
        return $"{size:0.##} {sizes[order]}";
    }
}
